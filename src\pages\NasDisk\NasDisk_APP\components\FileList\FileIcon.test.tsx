import { getFileType, FileTypes } from "@/utils/fileTypeUtils";

describe('File Icon Logic', () => {
  test('should return correct file type for different extensions', () => {
    // 测试PDF文件
    expect(getFileType('document.pdf')).toBe(FileTypes.PDF);
    
    // 测试压缩文件
    expect(getFileType('archive.zip')).toBe(FileTypes.ZIP);
    expect(getFileType('archive.rar')).toBe(FileTypes.ZIP);
    expect(getFileType('archive.7z')).toBe(FileTypes.ZIP);
    expect(getFileType('archive.tar')).toBe(FileTypes.ZIP);
    expect(getFileType('archive.gz')).toBe(FileTypes.ZIP);
    expect(getFileType('archive.bz2')).toBe(FileTypes.ZIP);
    
    // 测试Word文档
    expect(getFileType('document.doc')).toBe(FileTypes.WORD);
    expect(getFileType('document.docx')).toBe(FileTypes.WORD);
    expect(getFileType('document.rtf')).toBe(FileTypes.WORD);
    
    // 测试Excel文档
    expect(getFileType('spreadsheet.xls')).toBe(FileTypes.EXCEL);
    expect(getFileType('spreadsheet.xlsx')).toBe(FileTypes.EXCEL);
    expect(getFileType('data.csv')).toBe(FileTypes.EXCEL);
    
    // 测试PowerPoint文档
    expect(getFileType('presentation.ppt')).toBe(FileTypes.PPT);
    expect(getFileType('presentation.pptx')).toBe(FileTypes.PPT);
    
    // 测试文本文件
    expect(getFileType('readme.txt')).toBe(FileTypes.TEXT);
    expect(getFileType('config.json')).toBe(FileTypes.TEXT);
    expect(getFileType('style.css')).toBe(FileTypes.TEXT);
    expect(getFileType('script.js')).toBe(FileTypes.TEXT);
    expect(getFileType('README.md')).toBe(FileTypes.TEXT);
    
    // 测试音频文件
    expect(getFileType('song.mp3')).toBe(FileTypes.AUDIO);
    expect(getFileType('audio.wav')).toBe(FileTypes.AUDIO);
    
    // 测试图片文件
    expect(getFileType('image.jpg')).toBe(FileTypes.IMAGE);
    expect(getFileType('photo.png')).toBe(FileTypes.IMAGE);
    
    // 测试视频文件
    expect(getFileType('video.mp4')).toBe(FileTypes.VIDEO);
    expect(getFileType('movie.avi')).toBe(FileTypes.VIDEO);
    
    // 测试文件夹
    expect(getFileType('folder', true)).toBe(FileTypes.FOLDER);
    
    // 测试未知类型
    expect(getFileType('unknown.xyz')).toBe(FileTypes.UNKNOWN);
  });

  test('should handle case insensitive extensions', () => {
    expect(getFileType('Document.PDF')).toBe(FileTypes.PDF);
    expect(getFileType('Archive.ZIP')).toBe(FileTypes.ZIP);
    expect(getFileType('Presentation.PPTX')).toBe(FileTypes.PPT);
  });

  test('should handle files without extensions', () => {
    expect(getFileType('README')).toBe(FileTypes.UNKNOWN);
    expect(getFileType('config')).toBe(FileTypes.UNKNOWN);
  });
});
